import { Badge, type BadgeConfig } from "@components/common";
import { cn } from "@utils/cn";

export type ChanneType = "facebook" | "line" | "instagram" | "tiktok";

interface ChannelBadgeConfig extends BadgeConfig {
  type: ChanneType;
}

const CHANNEL_BADGE_CONFIG: Record<ChanneType, ChannelBadgeConfig> = {
  facebook: {
    containerClasses: "text-blue-500",
    icon: <i className="ri-facebook-circle-fill text-xl" />,
    iconAlt: "Facebook",
    type: "facebook",
  },
  instagram: {
    containerClasses: "text-pink-500",
    icon: <i className="ri-instagram-fill text-xl" />,
    iconAlt: "Instagram",
    type: "instagram",
  },
  line: {
    containerClasses: "text-green-500",
    icon: <i className="ri-line-fill text-xl" />,
    iconAlt: "Line",
    type: "line",
  },
  tiktok: {
    containerClasses: "text-black",
    icon: <i className="ri-tiktok-fill text-xl" />,
    iconAlt: "Tiktok",
    type: "tiktok",
  },
};

interface ChannelBadgeProps {
  type?: ChanneType;
  className?: string;
}

export const ChannelBadge = ({ type, className }: ChannelBadgeProps) => {
  return (
    <Badge type={type} className={cn("bg-base-200", className)} config={CHANNEL_BADGE_CONFIG} />
  );
};

export const CONTACT_CHANNEL_OPTIONS = (
  ["line", "facebook", "instagram", "tiktok"] as ChanneType[]
).map((type) => ({
  label: <ChannelBadge type={type} />,
  value: type,
}));
