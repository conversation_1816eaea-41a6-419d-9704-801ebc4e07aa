import { Badge, type BadgeConfig } from "@components/common";
import { cn } from "@utils/cn";

export type LeadStatusType = "active" | "completed" | "draft" | "suspended";

interface LeadStatusBadgeConfig extends BadgeConfig {
  type: LeadStatusType;
}

const LEAD_STATUS_BADGE_CONFIG: Record<LeadStatusType, LeadStatusBadgeConfig> = {
  active: {
    containerClasses: "bg-rose-50 !text-label-xs",
    icon: <div className="size-2 rounded-full bg-error" />,
    iconAlt: "Active",
    label: "ACTIVE",
    textClasses: "text-secondary",
    type: "active",
  },
  completed: {
    containerClasses: "bg-success-content !text-label-xs",
    icon: <div className="size-2 rounded-full bg-primary" />,
    iconAlt: "Completed",
    label: "COMPLETED",
    textClasses: "text-primary",
    type: "completed",
  },
  draft: {
    containerClasses: "bg-amber-50",
    icon: <div className="size-2 rounded-full bg-warning-content" />,
    iconAlt: "Draft",
    label: "DRAFT",
    textClasses: "text-warning-content",
    type: "draft",
  },
  suspended: {
    containerClasses: "bg-base-200",
    icon: <div className="size-2 rounded-full bg-neutral" />,
    iconAlt: "Suspended",
    label: "SUSPENDED",
    textClasses: "text-neutral",
    type: "suspended",
  },
};

interface LeadStatusBadgeProps {
  type?: LeadStatusType;
  className?: string;
}

export const LeadStatusBadge = ({ type, className }: LeadStatusBadgeProps) => {
  return (
    <Badge
      type={type}
      className={cn("border-neutral", className)}
      config={LEAD_STATUS_BADGE_CONFIG}
    />
  );
};

export const LEAD_STATUS_OPTIONS = (
  ["active", "completed", "draft", "suspended"] as LeadStatusType[]
).map((type) => ({
  label: <LeadStatusBadge type={type} />,
  value: type,
}));
