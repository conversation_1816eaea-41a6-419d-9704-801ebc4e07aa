import { Badge, type BadgeConfig } from "@components/common";
import { cn } from "@utils/cn";

export type TaskStatusType = "completed" | "onboarding";

interface TaskStatusBadgeConfig extends BadgeConfig {
  type: TaskStatusType;
}

const TASK_STATUS_BADGE_CONFIG: Record<TaskStatusType, TaskStatusBadgeConfig> = {
  completed: {
    containerClasses: "bg-success-content !text-label-xs",
    icon: <div className="size-2 rounded-full bg-primary-content" />,
    iconAlt: "completed",
    type: "completed",
  },
  onboarding: {
    containerClasses: "bg-rose-50 !text-label-xs",
    icon: <div className="size-2 rounded-full bg-secondary" />,
    iconAlt: "onboarding",
    type: "onboarding",
  },
};

interface TaskStatusBadgeProps {
  type: TaskStatusType;
  className?: string;
  label?: string;
}

export const TaskStatusBadge = ({ type, className, label }: TaskStatusBadgeProps) => {
  const badgeConfig: Record<TaskStatusType, TaskStatusBadgeConfig> = label
    ? {
        ...TASK_STATUS_BADGE_CONFIG,
        [type]: {
          ...TASK_STATUS_BADGE_CONFIG[type],
          label,
        },
      }
    : TASK_STATUS_BADGE_CONFIG;

  return (
    <Badge
      type={type}
      className={cn("border-none, text-base-content", className)}
      config={badgeConfig}
    />
  );
};

export const TASK_STATUS_OPTIONS = (["completed", "onboarding"] as TaskStatusType[]).map(
  (type) => ({
    label: <TaskStatusBadge type={type} />,
    value: type,
  }),
);
