import type { LeadProps } from "./interface";

const today = new Intl.DateTimeFormat("th-TH", {
  day: "2-digit",
  month: "2-digit",
  year: "2-digit",
}).format(new Date());

export const leads: LeadProps[] = [
  {
    contactChannel: "line",
    followUpDate: today,
    followUpStatus: "contacted",
    leadStatus: "draft",
    name: "<PERSON>",
    opportunity: "hot",
    servicesOfInterest: "botox",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    contactChannel: "instagram",
    followUpDate: today,
    followUpStatus: "interested",
    leadStatus: "active",
    name: "<PERSON>",
    opportunity: "hot",
    servicesOfInterest: "hifu",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    contactChannel: "tiktok",
    followUpDate: today,
    followUpStatus: "pending",
    leadStatus: "completed",
    name: "<PERSON>-Re-Mi",
    opportunity: "warm",
    servicesOfInterest: "thermage",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: true, task: "Follow up" },
    ],
  },
  {
    contactChannel: "facebook",
    followUpDate: today,
    followUpStatus: "not_interested",
    leadStatus: "suspended",
    name: "John Doreamon",
    opportunity: "cold",
    servicesOfInterest: "juvelook",
    tasks: [
      { completed: false, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
];
