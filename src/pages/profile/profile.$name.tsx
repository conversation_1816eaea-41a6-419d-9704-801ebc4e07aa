import { LeadAvatar } from "@components/avatar";
import { FollowUpBadge } from "@components/badge";
import { Container } from "@components/common";
import { TagChevronIcon } from "@phosphor-icons/react";
import { useParams } from "@tanstack/react-router";
// import { leadProfileField } from "./prolfileFields";

const TaskDetail = () => {
  return (
    <Container>
      <div className="border-container">
        <h6>Task</h6>
      </div>
    </Container>
  );
};

const LeadDetail = ({ name }: { name: string }) => {
  return (
    <Container className="w-5/12">
      <div className="border-container border-none">
        <div className="flex gap-6">
          <LeadAvatar />
          <div className="flex flex-col gap-2">
            <h5>{name}</h5>
            <div className="flex gap-2">
              <TagChevronIcon size={20} className="text-info" />
              <FollowUpBadge type="interested" />
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          {/* {leadProfileField.map((field) => (
            <h6 key={field.id}>{field.label}</h6>
          ))} */}
          <p>Opportunity</p>
          <p>Services of Interest</p>
          <p>Contact Channel</p>
          <p>Last Follow Up Date</p>
          <p>Follow Up Date</p>
          <p>Tatal day to next follow up</p>
          <p>Start Date</p>
          <p>Total date from start date</p>
          <p>Contact Info</p>
        </div>
        <div className="border-container">Note</div>
      </div>
    </Container>
  );
};

export function Profile() {
  const params = useParams({ strict: false }) as { name: string };
  const name = params.name;

  return (
    <>
      <TaskDetail />
      <LeadDetail name={name} />
    </>
  );
}
